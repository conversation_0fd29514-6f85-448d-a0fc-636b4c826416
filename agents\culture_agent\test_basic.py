#!/usr/bin/env python3
"""
基础测试脚本 - 验证核心数据结构和逻辑
不依赖外部包，仅测试基本功能
"""

import sys
import os
import json
from typing import Dict, List, Any, Optional
from enum import Enum

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_basic_imports():
    """测试基本导入"""
    print("=== 测试基本导入 ===")
    try:
        # 测试基本 Python 功能
        from typing import Dict, List, Any, Optional
        from enum import Enum
        print("✓ 基本类型导入成功")
        
        # 测试 JSON 处理
        test_data = {"test": "data", "number": 123}
        json_str = json.dumps(test_data)
        parsed_data = json.loads(json_str)
        print("✓ JSON 处理正常")
        
        return True
    except Exception as e:
        print(f"✗ 基本导入失败: {e}")
        return False

def test_workflow_enum():
    """测试工作流程枚举"""
    print("\n=== 测试工作流程枚举 ===")
    try:
        class WorkflowStep(str, Enum):
            INPUT_COMPANY = "input_company"
            SEARCH_MATERIALS = "search_materials"
            USER_CONFIRM = "user_confirm"
            ASK_QUESTIONS = "ask_questions"
            USER_ANSWERS = "user_answers"
            GENERATE_SYMBOLS = "generate_symbols"
            COMPLETED = "completed"
        
        # 测试枚举值
        step = WorkflowStep.INPUT_COMPANY
        print(f"✓ 当前步骤: {step}")
        print(f"✓ 步骤值: {step.value}")
        
        # 测试所有步骤
        all_steps = list(WorkflowStep)
        print(f"✓ 总共 {len(all_steps)} 个步骤")
        for i, step in enumerate(all_steps, 1):
            print(f"  {i}. {step.value}")
        
        return True
    except Exception as e:
        print(f"✗ 工作流程枚举测试失败: {e}")
        return False

def test_data_structures():
    """测试数据结构"""
    print("\n=== 测试数据结构 ===")
    try:
        # 模拟 CompanyInfo
        company_info = {
            "name": "腾讯科技",
            "industry": "互联网",
            "description": "中国领先的互联网增值服务提供商",
            "founded_year": 1998,
            "size": "大型企业"
        }
        print(f"✓ 企业信息: {company_info['name']}")
        
        # 模拟 SearchResult
        search_results = [
            {
                "title": "腾讯企业文化",
                "content": "腾讯秉承'用户为本，科技向善'的使命...",
                "source": "腾讯官网",
                "relevance_score": 0.95
            },
            {
                "title": "腾讯发展历程",
                "content": "1998年成立以来，腾讯从即时通讯起步...",
                "source": "企业年报",
                "relevance_score": 0.88
            }
        ]
        print(f"✓ 搜索结果: {len(search_results)} 条")
        
        # 模拟 Question
        questions = [
            {
                "id": 1,
                "question": "腾讯在发展过程中最重要的价值观是什么？",
                "category": "价值观与理念"
            },
            {
                "id": 2,
                "question": "腾讯有哪些独特的企业文化传统？",
                "category": "发展历程与传统"
            },
            {
                "id": 3,
                "question": "腾讯在创新方面有什么特色？",
                "category": "企业特色与创新"
            }
        ]
        print(f"✓ 生成问题: {len(questions)} 个")
        
        # 模拟 Answer
        answers = [
            {"question_id": 1, "answer": "用户为本，科技向善"},
            {"question_id": 2, "answer": "开放协作，敏捷创新"},
            {"question_id": 3, "answer": "连接一切，创造价值"}
        ]
        print(f"✓ 用户回答: {len(answers)} 个")
        
        # 模拟 CultureSymbol
        symbols = [
            {
                "name": "企鹅精神",
                "description": "以企鹅为象征，体现团队协作和用户关怀的企业文化",
                "meaning": "象征着腾讯团结协作、关爱用户的核心价值观",
                "visual_elements": ["企鹅形象", "蓝色主调", "圆润设计"],
                "symbolic_value": "团结协作，用户至上"
            },
            {
                "name": "连接之桥",
                "description": "象征腾讯连接人与人、人与服务的桥梁作用",
                "meaning": "体现腾讯作为连接器的使命和价值",
                "visual_elements": ["桥梁造型", "连接线条", "流动感"],
                "symbolic_value": "连接一切，创造价值"
            },
            {
                "name": "科技之光",
                "description": "代表腾讯以科技向善照亮未来的愿景",
                "meaning": "象征科技创新和社会责任的结合",
                "visual_elements": ["光芒效果", "科技感线条", "向上箭头"],
                "symbolic_value": "科技向善，照亮未来"
            }
        ]
        print(f"✓ 文化符号: {len(symbols)} 个")
        
        return True
    except Exception as e:
        print(f"✗ 数据结构测试失败: {e}")
        return False

def test_workflow_logic():
    """测试工作流程逻辑"""
    print("\n=== 测试工作流程逻辑 ===")
    try:
        # 模拟状态机逻辑
        class MockAgentState:
            def __init__(self):
                self.current_step = "input_company"
                self.company_info = None
                self.search_results = []
                self.materials_confirmed = False
                self.questions = []
                self.answers = []
                self.culture_symbols = []
                self.waiting_for_user = True
                self.message_history = []
            
            def can_proceed_to_next_step(self):
                if self.current_step == "input_company":
                    return self.company_info is not None
                elif self.current_step == "search_materials":
                    return len(self.search_results) > 0
                elif self.current_step == "user_confirm":
                    return self.materials_confirmed
                elif self.current_step == "ask_questions":
                    return len(self.questions) == 3
                elif self.current_step == "user_answers":
                    return len(self.answers) == 3
                elif self.current_step == "generate_symbols":
                    return len(self.culture_symbols) == 3
                return False
            
            def get_current_step_name(self):
                step_names = {
                    "input_company": "输入客户名称",
                    "search_materials": "搜索资料",
                    "user_confirm": "用户确认资料",
                    "ask_questions": "反问3个问题",
                    "user_answers": "用户回答问题",
                    "generate_symbols": "生成3个文化符号",
                    "completed": "流程完成"
                }
                return step_names.get(self.current_step, "未知步骤")
        
        # 测试状态转换
        state = MockAgentState()
        print(f"✓ 初始状态: {state.get_current_step_name()}")
        print(f"✓ 等待用户输入: {state.waiting_for_user}")
        print(f"✓ 可以进入下一步: {state.can_proceed_to_next_step()}")
        
        # 模拟步骤1: 输入企业名称
        state.company_info = {"name": "腾讯"}
        print(f"✓ 步骤1完成，可以进入下一步: {state.can_proceed_to_next_step()}")
        
        # 模拟步骤2: 搜索资料
        state.current_step = "search_materials"
        state.search_results = [{"title": "test", "content": "test"}]
        print(f"✓ 步骤2完成，可以进入下一步: {state.can_proceed_to_next_step()}")
        
        # 模拟步骤3: 用户确认
        state.current_step = "user_confirm"
        state.materials_confirmed = True
        print(f"✓ 步骤3完成，可以进入下一步: {state.can_proceed_to_next_step()}")
        
        return True
    except Exception as e:
        print(f"✗ 工作流程逻辑测试失败: {e}")
        return False

def test_json_processing():
    """测试 JSON 处理"""
    print("\n=== 测试 JSON 处理 ===")
    try:
        # 测试问题生成的 JSON 格式
        questions_json = '''
        [
            {
                "id": 1,
                "question": "贵公司在发展过程中最重要的价值观是什么？",
                "category": "价值观与理念"
            },
            {
                "id": 2,
                "question": "贵公司有哪些独特的传统做法？",
                "category": "发展历程与传统"
            },
            {
                "id": 3,
                "question": "贵公司在创新方面有什么特色？",
                "category": "企业特色与创新"
            }
        ]
        '''
        
        questions = json.loads(questions_json)
        print(f"✓ 问题 JSON 解析成功: {len(questions)} 个问题")
        
        # 测试符号生成的 JSON 格式
        symbols_json = '''
        [
            {
                "name": "创新之翼",
                "description": "象征企业在创新道路上的飞翔",
                "meaning": "代表企业不断创新、勇于突破的精神",
                "visual_elements": ["翅膀", "向上飞翔", "动感线条"],
                "symbolic_value": "创新突破，展翅高飞"
            }
        ]
        '''
        
        symbols = json.loads(symbols_json)
        print(f"✓ 符号 JSON 解析成功: {len(symbols)} 个符号")
        
        # 测试用户回答的 JSON 格式
        answers_json = '{"1": "用户为本", "2": "开放协作", "3": "科技向善"}'
        answers_dict = json.loads(answers_json)
        print(f"✓ 回答 JSON 解析成功: {len(answers_dict)} 个回答")
        
        return True
    except Exception as e:
        print(f"✗ JSON 处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("企业文化精神符号总结 Agent - 基础功能测试")
    print("=" * 50)
    
    tests = [
        test_basic_imports,
        test_workflow_enum,
        test_data_structures,
        test_workflow_logic,
        test_json_processing
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ 测试 {test_func.__name__} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基础测试通过！")
        print("\n下一步:")
        print("1. 安装依赖包: pip install -r requirements.txt")
        print("2. 设置环境变量: export OPENAI_API_KEY=your-key")
        print("3. 启动服务: uvicorn main:app --reload")
        return True
    else:
        print("❌ 部分测试失败，请检查代码")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
