我通过LangChain、LangGraph、AG-UI 协议来开发了多款Agent，放在了项目中Agents/文件夹下面，每一个Agent都是一个文件夹。
我现在想再开发一个web界面分为顶部菜音区域+四列主区域。
顶部菜单区域从左至右依次为：
  - logo
  - 当前导航路径+标题
  - 右对齐的当前主题（随系统、浅色模式、深色模式）
  - 右对齐的当前登录用户信息
四列主区域从左至右依次为：
  - 导航(竖向排列，icon+文字方式，可左右缩放)：Agents、知识库、设置
  - Agent列表（选中Agents）、知识库分类（选中知识库）、设置分类（选中设置）
  - Agent对话区（选中Agents）、知库库列表（选中知识库）、设置项（选中设置）
  - 当前Agent的使用工具区或，该区域随Agent的不同而发生相应的变化（只在Agents选中时出现，其它导航选中时不出现）
在Agents列表区域内有一个添加Agent、编辑Agents的功能。其中添加Agent点击时读取Agents/文件夹下所有Agent，并判断是否已加载，如未加载用户可点击“加载”按钮进行加载Agent到列表，然后点击已加载的Agent进行对话。