"""
企业文化精神符号总结 Agent 工具函数
实现搜索资料、反问问题、生成文化符号的核心功能
"""

import json
import asyncio
from typing import List, Dict, Any, Optional
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage

from schema import (
    AgentState, CompanyInfo, SearchResult, Question,
    Answer, CultureSymbol, WorkflowStep
)
from prompts.questions_prompt import QUESTIONS_GENERATION_PROMPT
from prompts.symbol_prompt import SYMBOL_GENERATION_PROMPT


class CultureAgentTools:
    """企业文化符号总结 Agent 工具类"""
    
    def __init__(self, openai_api_key: str, model_name: str = "deepseek-chat"):
        self.llm = ChatOpenAI(
            api_key=openai_api_key,
            model=model_name,
            base_url="https://api.deepseek.com/v1",
            temperature=0.7
        )
    
    async def extract_company_info(self, company_name: str) -> CompanyInfo:
        """从公司名称提取基本信息"""
        try:
            # 这里可以集成真实的企业信息API，目前使用LLM推理
            prompt = f"""
            请根据公司名称"{company_name}"，推理并提供该公司的基本信息。
            如果是知名公司，请提供准确信息；如果不确定，请标注为推测。
            
            请以JSON格式返回：
            {{
                "name": "公司全称",
                "industry": "所属行业",
                "description": "公司简介",
                "founded_year": 年份或null,
                "size": "公司规模"
            }}
            """
            
            response = await self.llm.ainvoke([HumanMessage(content=prompt)])
            result = json.loads(response.content)
            
            return CompanyInfo(**result)
            
        except Exception as e:
            # 如果解析失败，返回基本信息
            return CompanyInfo(
                name=company_name,
                industry="未知",
                description=f"{company_name}的企业信息",
                founded_year=None,
                size="未知"
            )
    
    async def search_company_materials(self, company_info: CompanyInfo) -> List[SearchResult]:
        """搜索企业相关资料"""
        try:
            # 模拟搜索结果 - 在实际应用中可以集成真实的搜索API
            search_prompt = f"""
            请为"{company_info.name}"生成3-5条相关的企业文化资料。
            这些资料应该包括：
            1. 企业发展历程
            2. 核心价值观
            3. 企业文化特色
            4. 重要事件或成就
            5. 创始人理念或企业故事
            
            请以JSON格式返回搜索结果：
            [
                {{
                    "title": "资料标题",
                    "content": "详细内容（200-300字）",
                    "source": "资料来源",
                    "relevance_score": 0.9
                }}
            ]
            """
            
            response = await self.llm.ainvoke([HumanMessage(content=search_prompt)])
            results_data = json.loads(response.content)
            
            search_results = []
            for item in results_data:
                search_results.append(SearchResult(**item))
            
            return search_results
            
        except Exception as e:
            # 返回默认搜索结果
            return [
                SearchResult(
                    title=f"{company_info.name}企业文化概述",
                    content=f"{company_info.name}是一家{company_info.industry}企业，致力于为客户提供优质的产品和服务。",
                    source="企业官网",
                    relevance_score=0.8
                )
            ]
    
    async def generate_questions(self, company_info: CompanyInfo, search_results: List[SearchResult]) -> List[Question]:
        """基于搜索结果生成3个反问问题"""
        try:
            # 整理搜索资料
            materials_text = "\n\n".join([
                f"标题：{result.title}\n内容：{result.content}\n来源：{result.source}"
                for result in search_results
            ])
            
            # 生成问题
            prompt = QUESTIONS_GENERATION_PROMPT.format(
                company_name=company_info.name,
                industry=company_info.industry or "未知",
                description=company_info.description or "暂无描述",
                search_materials=materials_text
            )
            
            response = await self.llm.ainvoke([HumanMessage(content=prompt)])
            
            # 提取JSON部分
            content = response.content
            start_idx = content.find('[')
            end_idx = content.rfind(']') + 1
            json_str = content[start_idx:end_idx]
            
            questions_data = json.loads(json_str)
            
            questions = []
            for item in questions_data:
                questions.append(Question(**item))
            
            return questions
            
        except Exception as e:
            # 返回默认问题
            return [
                Question(
                    id=1,
                    question=f"{company_info.name}在发展过程中，最重要的价值观或理念是什么？",
                    category="价值观与理念"
                ),
                Question(
                    id=2,
                    question=f"{company_info.name}有哪些独特的传统做法或企业文化活动？",
                    category="发展历程与传统"
                ),
                Question(
                    id=3,
                    question=f"{company_info.name}在创新发展方面有什么特色或优势？",
                    category="企业特色与创新"
                )
            ]
    
    async def generate_culture_symbols(
        self, 
        company_info: CompanyInfo, 
        search_results: List[SearchResult],
        answers: List[Answer]
    ) -> List[CultureSymbol]:
        """基于企业资料和用户回答生成3个文化符号"""
        try:
            # 整理搜索资料摘要
            materials_summary = "\n".join([
                f"• {result.title}：{result.content[:100]}..."
                for result in search_results[:3]
            ])
            
            # 整理用户回答
            answers_text = "\n".join([
                f"问题{answer.question_id}：{answer.answer}"
                for answer in answers
            ])
            
            # 生成符号
            prompt = SYMBOL_GENERATION_PROMPT.format(
                company_name=company_info.name,
                industry=company_info.industry or "未知",
                description=company_info.description or "暂无描述",
                search_materials_summary=materials_summary,
                user_answers=answers_text
            )
            
            response = await self.llm.ainvoke([HumanMessage(content=prompt)])
            
            # 提取JSON部分
            content = response.content
            start_idx = content.find('[')
            end_idx = content.rfind(']') + 1
            json_str = content[start_idx:end_idx]
            
            symbols_data = json.loads(json_str)
            
            symbols = []
            for item in symbols_data:
                symbols.append(CultureSymbol(**item))
            
            return symbols
            
        except Exception as e:
            # 返回默认符号
            return [
                CultureSymbol(
                    name="企业之光",
                    description=f"{company_info.name}如同一束光芒，照亮行业发展的道路，体现了企业的引领作用和创新精神。",
                    meaning="象征企业在行业中的领导地位和对未来的照亮作用。",
                    visual_elements=["光芒", "向上箭头", "明亮色彩"],
                    symbolic_value="引领创新，照亮未来"
                ),
                CultureSymbol(
                    name="传承之根",
                    description=f"{company_info.name}深深扎根于行业土壤，传承优秀传统，为企业发展提供坚实基础。",
                    meaning="体现企业深厚的历史底蕴和传承精神。",
                    visual_elements=["树根", "大地", "稳重色调"],
                    symbolic_value="传承历史，根基深厚"
                ),
                CultureSymbol(
                    name="协作之桥",
                    description=f"{company_info.name}架起沟通合作的桥梁，连接各方资源，实现共赢发展。",
                    meaning="象征企业的合作精神和桥梁作用。",
                    visual_elements=["桥梁", "连接线", "和谐色彩"],
                    symbolic_value="合作共赢，连接未来"
                )
            ]


# 工具函数的异步包装器
async def process_company_input(state: AgentState, company_name: str, tools: CultureAgentTools) -> AgentState:
    """处理公司名称输入"""
    try:
        company_info = await tools.extract_company_info(company_name)
        state.company_info = company_info
        state.current_step = WorkflowStep.SEARCH_MATERIALS
        state.add_message("info", f"已识别企业：{company_info.name}")
        return state
    except Exception as e:
        state.error_message = f"处理企业信息时出错：{str(e)}"
        return state


async def search_materials(state: AgentState, tools: CultureAgentTools) -> AgentState:
    """搜索企业资料"""
    try:
        if not state.company_info:
            state.error_message = "缺少企业信息"
            return state
            
        search_results = await tools.search_company_materials(state.company_info)
        state.search_results = search_results
        state.current_step = WorkflowStep.USER_CONFIRM
        state.add_message("search_results", "已搜索到相关企业资料")
        return state
    except Exception as e:
        state.error_message = f"搜索资料时出错：{str(e)}"
        return state


async def generate_questions_step(state: AgentState, tools: CultureAgentTools) -> AgentState:
    """生成反问问题"""
    try:
        if not state.materials_confirmed:
            state.error_message = "用户尚未确认资料"
            return state
            
        questions = await tools.generate_questions(state.company_info, state.search_results)
        state.questions = questions
        state.current_step = WorkflowStep.USER_ANSWERS
        state.add_message("questions", "已生成3个问题")
        return state
    except Exception as e:
        state.error_message = f"生成问题时出错：{str(e)}"
        return state


async def generate_symbols_step(state: AgentState, tools: CultureAgentTools) -> AgentState:
    """生成文化符号"""
    try:
        if len(state.answers) != 3:
            state.error_message = "用户尚未完成所有问题的回答"
            return state
            
        symbols = await tools.generate_culture_symbols(
            state.company_info, 
            state.search_results, 
            state.answers
        )
        state.culture_symbols = symbols
        state.current_step = WorkflowStep.COMPLETED
        state.waiting_for_user = False
        state.add_message("symbols", "已生成3个文化符号")
        return state
    except Exception as e:
        state.error_message = f"生成符号时出错：{str(e)}"
        return state
