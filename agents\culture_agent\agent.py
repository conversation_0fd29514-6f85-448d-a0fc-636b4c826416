"""
企业文化精神符号总结 Agent - LangGraph 状态机实现
严格按照 6 步流程执行，每步等待用户确认或输入
"""

import asyncio
from typing import Dict, Any, Optional
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from schema import AgentState, WorkflowStep, Answer
from tools import (
    CultureAgentTools,
    process_company_input,
    search_materials,
    generate_questions_step,
    generate_symbols_step
)


class CultureSymbolAgent:
    """企业文化精神符号总结 Agent"""
    
    def __init__(self, openai_api_key: str, model_name: str = "deepseek-chat"):
        self.tools = CultureAgentTools(openai_api_key, model_name)
        self.memory = MemorySaver()
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """构建 LangGraph 状态机"""
        
        # 创建状态图
        workflow = StateGraph(AgentState)
        
        # 添加节点
        workflow.add_node("start", self._start_node)
        workflow.add_node("input_company", self._input_company_node)
        workflow.add_node("search_materials", self._search_materials_node)
        workflow.add_node("confirm_materials", self._confirm_materials_node)
        workflow.add_node("ask_questions", self._ask_questions_node)
        workflow.add_node("collect_answers", self._collect_answers_node)
        workflow.add_node("generate_symbols", self._generate_symbols_node)
        workflow.add_node("complete", self._complete_node)
        
        # 设置入口点
        workflow.set_entry_point("start")
        
        # 添加边和条件路由
        workflow.add_edge("start", "input_company")
        
        workflow.add_conditional_edges(
            "input_company",
            self._should_proceed_to_search,
            {
                "search": "search_materials",
                "wait": "input_company"
            }
        )
        
        workflow.add_conditional_edges(
            "search_materials",
            self._should_proceed_to_confirm,
            {
                "confirm": "confirm_materials",
                "wait": "search_materials"
            }
        )
        
        workflow.add_conditional_edges(
            "confirm_materials",
            self._should_proceed_to_questions,
            {
                "questions": "ask_questions",
                "wait": "confirm_materials"
            }
        )
        
        workflow.add_conditional_edges(
            "ask_questions",
            self._should_proceed_to_answers,
            {
                "answers": "collect_answers",
                "wait": "ask_questions"
            }
        )
        
        workflow.add_conditional_edges(
            "collect_answers",
            self._should_proceed_to_symbols,
            {
                "symbols": "generate_symbols",
                "wait": "collect_answers"
            }
        )
        
        workflow.add_conditional_edges(
            "generate_symbols",
            self._should_complete,
            {
                "complete": "complete",
                "wait": "generate_symbols"
            }
        )
        
        workflow.add_edge("complete", END)
        
        return workflow.compile(checkpointer=self.memory)
    
    # 节点实现
    async def _start_node(self, state: AgentState) -> AgentState:
        """开始节点"""
        state.add_message("system", "欢迎使用企业文化精神符号总结 Agent！")
        state.add_message("instruction", "请输入您要分析的企业名称：")
        state.current_step = WorkflowStep.INPUT_COMPANY
        state.waiting_for_user = True
        return state
    
    async def _input_company_node(self, state: AgentState) -> AgentState:
        """输入企业名称节点"""
        if state.waiting_for_user:
            return state
        
        # 这里应该从用户输入中获取企业名称
        # 在实际WebSocket实现中，这个信息会通过外部调用传入
        return state
    
    async def _search_materials_node(self, state: AgentState) -> AgentState:
        """搜索资料节点"""
        if not state.company_info:
            return state
            
        state = await search_materials(state, self.tools)
        return state
    
    async def _confirm_materials_node(self, state: AgentState) -> AgentState:
        """确认资料节点"""
        if state.waiting_for_user:
            return state
        return state
    
    async def _ask_questions_node(self, state: AgentState) -> AgentState:
        """生成问题节点"""
        if not state.materials_confirmed:
            return state
            
        state = await generate_questions_step(state, self.tools)
        return state
    
    async def _collect_answers_node(self, state: AgentState) -> AgentState:
        """收集回答节点"""
        if state.waiting_for_user:
            return state
        return state
    
    async def _generate_symbols_node(self, state: AgentState) -> AgentState:
        """生成符号节点"""
        if len(state.answers) != 3:
            return state
            
        state = await generate_symbols_step(state, self.tools)
        return state
    
    async def _complete_node(self, state: AgentState) -> AgentState:
        """完成节点"""
        state.add_message("system", "企业文化精神符号总结完成！")
        state.waiting_for_user = False
        return state
    
    # 条件判断函数
    def _should_proceed_to_search(self, state: AgentState) -> str:
        """判断是否可以进入搜索阶段"""
        if state.company_info and not state.waiting_for_user:
            return "search"
        return "wait"
    
    def _should_proceed_to_confirm(self, state: AgentState) -> str:
        """判断是否可以进入确认阶段"""
        if len(state.search_results) > 0:
            return "confirm"
        return "wait"
    
    def _should_proceed_to_questions(self, state: AgentState) -> str:
        """判断是否可以进入问题阶段"""
        if state.materials_confirmed:
            return "questions"
        return "wait"
    
    def _should_proceed_to_answers(self, state: AgentState) -> str:
        """判断是否可以进入回答阶段"""
        if len(state.questions) == 3:
            return "answers"
        return "wait"
    
    def _should_proceed_to_symbols(self, state: AgentState) -> str:
        """判断是否可以进入符号生成阶段"""
        if len(state.answers) == 3:
            return "symbols"
        return "wait"
    
    def _should_complete(self, state: AgentState) -> str:
        """判断是否完成"""
        if len(state.culture_symbols) == 3:
            return "complete"
        return "wait"
    
    # 外部调用接口
    async def process_user_input(self, state: AgentState, user_input: str, input_type: str = "text") -> AgentState:
        """处理用户输入"""
        try:
            if state.current_step == WorkflowStep.INPUT_COMPANY:
                # 处理企业名称输入
                state = await process_company_input(state, user_input, self.tools)
                state.waiting_for_user = False
                
            elif state.current_step == WorkflowStep.USER_CONFIRM:
                # 处理用户确认
                if user_input.lower() in ['是', 'yes', 'y', '确认', '同意']:
                    state.materials_confirmed = True
                    state.current_step = WorkflowStep.ASK_QUESTIONS
                    state.waiting_for_user = False
                else:
                    state.add_message("system", "请确认搜索到的资料是否准确")
                    
            elif state.current_step == WorkflowStep.USER_ANSWERS:
                # 处理用户回答
                if input_type == "answers":
                    # 期望输入格式：{"1": "答案1", "2": "答案2", "3": "答案3"}
                    import json
                    try:
                        answers_dict = json.loads(user_input)
                        for q_id, answer_text in answers_dict.items():
                            answer = Answer(question_id=int(q_id), answer=answer_text)
                            state.answers.append(answer)
                        
                        if len(state.answers) == 3:
                            state.current_step = WorkflowStep.GENERATE_SYMBOLS
                            state.waiting_for_user = False
                    except:
                        state.add_message("error", "回答格式错误，请重新输入")
            
            return state
            
        except Exception as e:
            state.error_message = f"处理用户输入时出错：{str(e)}"
            return state
    
    async def run_step(self, state: AgentState, thread_id: str = "default") -> AgentState:
        """执行一个步骤"""
        try:
            config = {"configurable": {"thread_id": thread_id}}
            result = await self.graph.ainvoke(state, config=config)
            return result
        except Exception as e:
            state.error_message = f"执行步骤时出错：{str(e)}"
            return state
    
    async def get_current_response(self, state: AgentState) -> Dict[str, Any]:
        """获取当前步骤的响应"""
        response = {
            "step": state.current_step.value,
            "step_name": state.get_current_step_name(),
            "waiting_for_user": state.waiting_for_user,
            "error": state.error_message
        }
        
        if state.current_step == WorkflowStep.INPUT_COMPANY:
            response.update({
                "type": "input_request",
                "message": "请输入您要分析的企业名称：",
                "input_type": "company_name"
            })
            
        elif state.current_step == WorkflowStep.USER_CONFIRM:
            response.update({
                "type": "confirmation_request",
                "message": "以下是搜索到的企业资料，请确认是否准确：",
                "data": {
                    "company_info": state.company_info.dict() if state.company_info else None,
                    "search_results": [result.dict() for result in state.search_results]
                }
            })
            
        elif state.current_step == WorkflowStep.USER_ANSWERS:
            response.update({
                "type": "questions",
                "message": "请回答以下3个问题，以便更好地了解企业文化：",
                "data": {
                    "questions": [q.dict() for q in state.questions]
                }
            })
            
        elif state.current_step == WorkflowStep.COMPLETED:
            response.update({
                "type": "symbols",
                "message": "企业文化精神符号总结完成！",
                "data": {
                    "symbols": [symbol.dict() for symbol in state.culture_symbols]
                }
            })
        
        return response
