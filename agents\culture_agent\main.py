"""
企业文化精神符号总结 Agent - FastAPI + WebSocket 服务
实现符合 AG-UI 协议的 WebSocket 服务，处理用户交互
"""

import os
import json
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

from agent import CultureSymbolAgent
from schema import AgentState, WorkflowStep, WebSocketMessage, AGUIResponse

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建 FastAPI 应用
app = FastAPI(
    title="企业文化精神符号总结 Agent",
    description="基于 LangGraph + AG-UI 协议的企业文化符号生成服务",
    version="1.0.0"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
agent: Optional[CultureSymbolAgent] = None
active_sessions: Dict[str, Dict[str, Any]] = {}


class SessionManager:
    """会话管理器"""
    
    def __init__(self):
        self.sessions = {}
    
    def create_session(self, session_id: str) -> AgentState:
        """创建新会话"""
        state = AgentState()
        self.sessions[session_id] = {
            "state": state,
            "created_at": datetime.now(),
            "last_activity": datetime.now()
        }
        return state
    
    def get_session(self, session_id: str) -> Optional[AgentState]:
        """获取会话状态"""
        session = self.sessions.get(session_id)
        if session:
            session["last_activity"] = datetime.now()
            return session["state"]
        return None
    
    def update_session(self, session_id: str, state: AgentState):
        """更新会话状态"""
        if session_id in self.sessions:
            self.sessions[session_id]["state"] = state
            self.sessions[session_id]["last_activity"] = datetime.now()
    
    def remove_session(self, session_id: str):
        """移除会话"""
        if session_id in self.sessions:
            del self.sessions[session_id]


# 创建会话管理器
session_manager = SessionManager()


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global agent
    
    # 获取 DeepSeek API Key
    openai_api_key = os.getenv("OPENAI_API_KEY", "sk-2eace239cf454e75a8e16ed2e05405a7")
    if not openai_api_key:
        logger.error("OPENAI_API_KEY 环境变量未设置")
        raise ValueError("OPENAI_API_KEY 环境变量未设置")
    
    # 初始化 Agent
    try:
        agent = CultureSymbolAgent(openai_api_key)
        logger.info("企业文化精神符号总结 Agent 初始化成功")
    except Exception as e:
        logger.error(f"Agent 初始化失败: {e}")
        raise


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "企业文化精神符号总结 Agent API",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "agent_ready": agent is not None
    }


class ChatMessage(BaseModel):
    """聊天消息模型"""
    message: str
    session_id: Optional[str] = None
    input_type: str = "text"


@app.post("/chat")
async def chat_endpoint(chat_message: ChatMessage):
    """HTTP 聊天接口（用于测试）"""
    if not agent:
        raise HTTPException(status_code=500, detail="Agent 未初始化")
    
    session_id = chat_message.session_id or "default"
    
    # 获取或创建会话
    state = session_manager.get_session(session_id)
    if not state:
        state = session_manager.create_session(session_id)
        # 初始化状态
        state = await agent.run_step(state, session_id)
    
    # 处理用户输入
    if not state.waiting_for_user:
        return {"error": "当前不需要用户输入"}
    
    # 处理用户消息
    state = await agent.process_user_input(state, chat_message.message, chat_message.input_type)
    
    # 执行下一步
    state = await agent.run_step(state, session_id)
    
    # 更新会话
    session_manager.update_session(session_id, state)
    
    # 获取响应
    response = await agent.get_current_response(state)
    
    return response


@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket 连接端点 - 符合 AG-UI 协议"""
    await websocket.accept()
    logger.info(f"WebSocket 连接建立: {session_id}")
    
    if not agent:
        await websocket.send_json({
            "type": "error",
            "content": "Agent 未初始化",
            "timestamp": datetime.now().isoformat()
        })
        await websocket.close()
        return
    
    try:
        # 获取或创建会话
        state = session_manager.get_session(session_id)
        if not state:
            state = session_manager.create_session(session_id)
            # 初始化状态并发送欢迎消息
            state = await agent.run_step(state, session_id)
            session_manager.update_session(session_id, state)
            
            # 发送初始响应
            response = await agent.get_current_response(state)
            await send_agui_response(websocket, response)
        
        # 消息处理循环
        while True:
            try:
                # 接收用户消息
                data = await websocket.receive_json()
                logger.info(f"收到消息: {data}")
                
                # 解析消息
                message_type = data.get("type", "text")
                content = data.get("content", "")
                input_type = data.get("input_type", "text")
                
                # 获取当前状态
                state = session_manager.get_session(session_id)
                if not state:
                    await websocket.send_json({
                        "type": "error",
                        "content": "会话不存在",
                        "timestamp": datetime.now().isoformat()
                    })
                    continue
                
                # 检查是否等待用户输入
                if not state.waiting_for_user:
                    await websocket.send_json({
                        "type": "info",
                        "content": "当前不需要用户输入，请等待处理完成",
                        "timestamp": datetime.now().isoformat()
                    })
                    continue
                
                # 处理用户输入
                state = await agent.process_user_input(state, content, input_type)
                
                # 执行状态机步骤
                state = await agent.run_step(state, session_id)
                
                # 更新会话
                session_manager.update_session(session_id, state)
                
                # 获取并发送响应
                response = await agent.get_current_response(state)
                await send_agui_response(websocket, response)
                
            except WebSocketDisconnect:
                logger.info(f"WebSocket 连接断开: {session_id}")
                break
            except json.JSONDecodeError:
                await websocket.send_json({
                    "type": "error",
                    "content": "消息格式错误",
                    "timestamp": datetime.now().isoformat()
                })
            except Exception as e:
                logger.error(f"处理消息时出错: {e}")
                await websocket.send_json({
                    "type": "error",
                    "content": f"处理消息时出错: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                })
    
    except Exception as e:
        logger.error(f"WebSocket 连接错误: {e}")
    finally:
        # 清理会话（可选）
        # session_manager.remove_session(session_id)
        logger.info(f"WebSocket 连接结束: {session_id}")


async def send_agui_response(websocket: WebSocket, response: Dict[str, Any]):
    """发送符合 AG-UI 协议的响应"""
    try:
        # 构建 AG-UI 协议响应
        agui_response = {
            "type": response.get("type", "message"),
            "content": response.get("message", ""),
            "step": response.get("step"),
            "step_name": response.get("step_name"),
            "waiting_for_user": response.get("waiting_for_user", True),
            "timestamp": datetime.now().isoformat()
        }
        
        # 添加数据字段
        if "data" in response:
            agui_response["data"] = response["data"]
        
        # 添加错误信息
        if response.get("error"):
            agui_response["error"] = response["error"]
        
        await websocket.send_json(agui_response)
        logger.info(f"发送响应: {agui_response['type']}")
        
    except Exception as e:
        logger.error(f"发送响应时出错: {e}")


@app.get("/sessions")
async def list_sessions():
    """列出所有活跃会话"""
    sessions_info = {}
    for session_id, session_data in session_manager.sessions.items():
        state = session_data["state"]
        sessions_info[session_id] = {
            "current_step": state.current_step.value,
            "step_name": state.get_current_step_name(),
            "waiting_for_user": state.waiting_for_user,
            "created_at": session_data["created_at"].isoformat(),
            "last_activity": session_data["last_activity"].isoformat(),
            "company_name": state.company_info.name if state.company_info else None
        }
    
    return {
        "total_sessions": len(sessions_info),
        "sessions": sessions_info
    }


@app.delete("/sessions/{session_id}")
async def delete_session(session_id: str):
    """删除指定会话"""
    if session_id in session_manager.sessions:
        session_manager.remove_session(session_id)
        return {"message": f"会话 {session_id} 已删除"}
    else:
        raise HTTPException(status_code=404, detail="会话不存在")


if __name__ == "__main__":
    import uvicorn
    
    # 检查环境变量
    openai_api_key = os.getenv("OPENAI_API_KEY", "sk-2eace239cf454e75a8e16ed2e05405a7")
    
    # 启动服务
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )