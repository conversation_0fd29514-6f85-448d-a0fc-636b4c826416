"""
企业文化精神符号总结 Agent 数据模型
定义 AgentState 和相关的数据结构，管理 6 步流程的状态
"""

from typing import Dict, List, Optional, Any, Literal
from pydantic import BaseModel, Field
from enum import Enum


class WorkflowStep(str, Enum):
    """工作流程步骤枚举"""
    INPUT_COMPANY = "input_company"           # 步骤1: 输入客户名称
    SEARCH_MATERIALS = "search_materials"     # 步骤2: 搜索资料
    USER_CONFIRM = "user_confirm"             # 步骤3: 用户确认资料
    ASK_QUESTIONS = "ask_questions"           # 步骤4: 反问3个问题
    USER_ANSWERS = "user_answers"             # 步骤5: 用户回答问题
    GENERATE_SYMBOLS = "generate_symbols"     # 步骤6: 生成3个文化符号
    COMPLETED = "completed"                   # 流程完成


class UserMessage(BaseModel):
    """用户消息模型"""
    type: Literal["text", "confirmation", "answers"] = "text"
    content: str
    timestamp: Optional[str] = None


class AgentMessage(BaseModel):
    """Agent 消息模型"""
    type: Literal["text", "question", "confirmation_request", "symbols"] = "text"
    content: str
    data: Optional[Dict[str, Any]] = None
    timestamp: Optional[str] = None


class CompanyInfo(BaseModel):
    """公司信息模型"""
    name: str
    industry: Optional[str] = None
    description: Optional[str] = None
    founded_year: Optional[int] = None
    size: Optional[str] = None


class SearchResult(BaseModel):
    """搜索结果模型"""
    title: str
    content: str
    source: str
    relevance_score: Optional[float] = None


class Question(BaseModel):
    """反问问题模型"""
    id: int
    question: str
    category: str  # 如：价值观、发展历程、企业特色等


class Answer(BaseModel):
    """用户回答模型"""
    question_id: int
    answer: str


class CultureSymbol(BaseModel):
    """文化符号模型"""
    name: str
    description: str
    meaning: str
    visual_elements: List[str]
    symbolic_value: str


class AgentState(BaseModel):
    """Agent 状态模型 - 管理整个 6 步流程的状态"""
    
    # 当前流程步骤
    current_step: WorkflowStep = WorkflowStep.INPUT_COMPANY
    
    # 公司信息
    company_info: Optional[CompanyInfo] = None
    
    # 搜索到的资料
    search_results: List[SearchResult] = Field(default_factory=list)
    
    # 用户是否确认搜索结果
    materials_confirmed: bool = False
    
    # 反问的3个问题
    questions: List[Question] = Field(default_factory=list)
    
    # 用户的回答
    answers: List[Answer] = Field(default_factory=list)
    
    # 生成的3个文化符号
    culture_symbols: List[CultureSymbol] = Field(default_factory=list)
    
    # 消息历史
    message_history: List[Dict[str, Any]] = Field(default_factory=list)
    
    # 等待用户输入的标志
    waiting_for_user: bool = True
    
    # 错误信息
    error_message: Optional[str] = None
    
    # 额外的上下文信息
    context: Dict[str, Any] = Field(default_factory=dict)

    def add_message(self, message_type: str, content: str, sender: str = "agent", **kwargs):
        """添加消息到历史记录"""
        message = {
            "type": message_type,
            "content": content,
            "sender": sender,
            "timestamp": kwargs.get("timestamp"),
            **kwargs
        }
        self.message_history.append(message)

    def get_current_step_name(self) -> str:
        """获取当前步骤的中文名称"""
        step_names = {
            WorkflowStep.INPUT_COMPANY: "输入客户名称",
            WorkflowStep.SEARCH_MATERIALS: "搜索资料",
            WorkflowStep.USER_CONFIRM: "用户确认资料",
            WorkflowStep.ASK_QUESTIONS: "反问3个问题",
            WorkflowStep.USER_ANSWERS: "用户回答问题",
            WorkflowStep.GENERATE_SYMBOLS: "生成3个文化符号",
            WorkflowStep.COMPLETED: "流程完成"
        }
        return step_names.get(self.current_step, "未知步骤")

    def can_proceed_to_next_step(self) -> bool:
        """检查是否可以进入下一步"""
        if self.current_step == WorkflowStep.INPUT_COMPANY:
            return self.company_info is not None
        elif self.current_step == WorkflowStep.SEARCH_MATERIALS:
            return len(self.search_results) > 0
        elif self.current_step == WorkflowStep.USER_CONFIRM:
            return self.materials_confirmed
        elif self.current_step == WorkflowStep.ASK_QUESTIONS:
            return len(self.questions) == 3
        elif self.current_step == WorkflowStep.USER_ANSWERS:
            return len(self.answers) == 3
        elif self.current_step == WorkflowStep.GENERATE_SYMBOLS:
            return len(self.culture_symbols) == 3
        return False

    def reset_state(self):
        """重置状态到初始状态"""
        self.current_step = WorkflowStep.INPUT_COMPANY
        self.company_info = None
        self.search_results = []
        self.materials_confirmed = False
        self.questions = []
        self.answers = []
        self.culture_symbols = []
        self.message_history = []
        self.waiting_for_user = True
        self.error_message = None
        self.context = {}


class WebSocketMessage(BaseModel):
    """WebSocket 消息模型 - 符合 AG-UI 协议"""
    type: str
    data: Dict[str, Any]
    timestamp: Optional[str] = None


class AGUIResponse(BaseModel):
    """AG-UI 协议响应模型"""
    type: Literal["message", "question", "confirmation", "symbols", "error"]
    content: str
    data: Optional[Dict[str, Any]] = None
    step: Optional[str] = None
    waiting_for_user: bool = True
