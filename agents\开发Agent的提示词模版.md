### 开发Agent的提示词模版

【任务】用 Python 3.9+ 实现一个「企业文化精神符号总结 Agent」，要求：

1. 采用 LangGraph + AG-UI 协议（WebSocket）实现；
2. 严格按 6 步流程：输入客户名称→搜索资料→用户确认→反问3问题→用户回答→生成3个文化符号；
3. 每步必须等待用户确认或输入后才能继续；
4. 目录结构：
   culture_agent/
   ├─ agent.py        # LangGraph 状态机
   ├─ tools.py        # 搜索、反问、生成符号函数
   ├─ schema.py       # AgentState 数据模型
   ├─ main.py         # FastAPI + WebSocket 实现 AG-UI 协议
   ├─ prompts/
   │  ├─ symbol_prompt.py   # 符号生成 prompt
   │  └─ questions_prompt.py # 反问 3 问题 prompt
   └─ README.md       # 安装与启动说明

【依赖】
pip install langgraph langchain-openai fastapi "uvicorn[standard]" agui

【环境变量】
export OPENAI_API_KEY=<your-key>

【运行】
uvicorn main:app --reload
