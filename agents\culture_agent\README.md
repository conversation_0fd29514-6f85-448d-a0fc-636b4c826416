# 企业文化精神符号总结 Agent

基于 LangGraph + AG-UI 协议实现的企业文化精神符号生成服务，严格按照 6 步流程执行，每步等待用户确认或输入。

## 功能特性

- 🎯 **6步标准流程**：输入企业名称 → 搜索资料 → 用户确认 → 反问3问题 → 用户回答 → 生成3个文化符号
- 🔄 **状态机管理**：使用 LangGraph 实现严格的状态流转，确保每步等待用户交互
- 🌐 **WebSocket 支持**：符合 AG-UI 协议的实时通信
- 📊 **会话管理**：支持多用户并发，独立会话状态
- 🤖 **智能分析**：基于 GPT-4 的企业文化深度分析

## 项目结构

```
culture_agent/
├── agent.py              # LangGraph 状态机实现
├── tools.py              # 核心工具函数（搜索、问答、符号生成）
├── schema.py             # 数据模型定义
├── main.py               # FastAPI + WebSocket 服务
├── prompts/
│   ├── symbol_prompt.py     # 符号生成 Prompt 模板
│   └── questions_prompt.py  # 反问问题 Prompt 模板
└── README.md             # 项目文档
```

## 安装依赖

### 1. Python 环境要求
- Python 3.9+

### 2. 安装依赖包
```bash
pip install langgraph langchain-openai fastapi "uvicorn[standard]" agui pydantic
```

### 3. 环境变量配置
```bash
export OPENAI_API_KEY=<your-openai-api-key>
```

## 快速启动

### 1. 启动服务
```bash
cd culture_agent
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 2. 访问服务
- **API 文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **WebSocket 连接**: ws://localhost:8000/ws/{session_id}

## 使用方法

### WebSocket 客户端示例

```javascript
// 连接 WebSocket
const ws = new WebSocket('ws://localhost:8000/ws/my-session-id');

// 监听消息
ws.onmessage = function(event) {
    const response = JSON.parse(event.data);
    console.log('收到响应:', response);
    
    switch(response.type) {
        case 'input_request':
            // 需要输入企业名称
            break;
        case 'confirmation_request':
            // 需要确认搜索结果
            break;
        case 'questions':
            // 需要回答问题
            break;
        case 'symbols':
            // 获得最终符号结果
            break;
    }
};

// 发送消息
function sendMessage(content, inputType = 'text') {
    ws.send(JSON.stringify({
        type: 'message',
        content: content,
        input_type: inputType
    }));
}

// 使用示例
sendMessage('腾讯');  // 步骤1: 输入企业名称
sendMessage('是');    // 步骤3: 确认资料
sendMessage('{"1": "答案1", "2": "答案2", "3": "答案3"}', 'answers');  // 步骤5: 回答问题
```

### HTTP API 示例

```bash
# 测试聊天接口
curl -X POST "http://localhost:8000/chat" \
     -H "Content-Type: application/json" \
     -d '{
       "message": "腾讯",
       "session_id": "test-session",
       "input_type": "text"
     }'
```

## 6步流程详解

### 步骤1: 输入企业名称 (INPUT_COMPANY)
- **用户操作**: 输入要分析的企业名称
- **系统响应**: 识别企业基本信息
- **示例**: "腾讯" → 提取腾讯公司基本信息

### 步骤2: 搜索资料 (SEARCH_MATERIALS)
- **系统操作**: 自动搜索企业相关资料
- **搜索内容**: 企业发展历程、价值观、文化特色等
- **输出**: 3-5条相关资料摘要

### 步骤3: 用户确认 (USER_CONFIRM)
- **用户操作**: 确认搜索到的资料是否准确
- **确认方式**: 回复"是"、"确认"或"yes"
- **作用**: 确保后续分析基于准确信息

### 步骤4: 反问3问题 (ASK_QUESTIONS)
- **系统操作**: 基于搜索资料生成3个深度问题
- **问题维度**: 
  - 价值观与理念
  - 发展历程与传统  
  - 企业特色与创新
- **目的**: 获取更深层的文化信息

### 步骤5: 用户回答 (USER_ANSWERS)
- **用户操作**: 回答3个问题
- **格式要求**: JSON格式 `{"1": "答案1", "2": "答案2", "3": "答案3"}`
- **重要性**: 回答质量直接影响符号生成效果

### 步骤6: 生成3个文化符号 (GENERATE_SYMBOLS)
- **系统操作**: 基于所有信息生成3个文化符号
- **符号内容**: 
  - 符号名称和描述
  - 深层含义和文化价值
  - 视觉元素建议
  - 精神价值总结

## API 接口

### WebSocket 接口

#### 连接地址
```
ws://localhost:8000/ws/{session_id}
```

#### 消息格式
```json
{
    "type": "message",
    "content": "用户输入内容",
    "input_type": "text"  // 或 "answers"
}
```

#### 响应格式
```json
{
    "type": "input_request|confirmation_request|questions|symbols|error",
    "content": "响应内容",
    "step": "当前步骤",
    "step_name": "步骤中文名称",
    "waiting_for_user": true,
    "data": {},  // 额外数据
    "timestamp": "2024-01-01T00:00:00"
}
```

### HTTP 接口

#### POST /chat
测试用的聊天接口

#### GET /health
健康检查接口

#### GET /sessions
查看所有活跃会话

#### DELETE /sessions/{session_id}
删除指定会话

## 配置说明

### 环境变量
- `OPENAI_API_KEY`: OpenAI API 密钥（必需）

### 模型配置
默认使用 GPT-4 模型，可在 `agent.py` 中修改：
```python
agent = CultureSymbolAgent(openai_api_key, model_name="gpt-3.5-turbo")
```

## 开发指南

### 自定义 Prompt
修改 `prompts/` 目录下的文件来自定义提示模板：
- `questions_prompt.py`: 问题生成模板
- `symbol_prompt.py`: 符号生成模板

### 扩展工具函数
在 `tools.py` 中添加新的工具函数，如集成真实的搜索API。

### 状态机扩展
在 `agent.py` 中修改状态机逻辑，添加新的步骤或条件判断。

## 故障排除

### 常见问题

1. **OPENAI_API_KEY 未设置**
   ```bash
   export OPENAI_API_KEY=your-api-key
   ```

2. **依赖包安装失败**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

3. **WebSocket 连接失败**
   - 检查防火墙设置
   - 确认端口8000未被占用

### 日志查看
服务启动后会输出详细日志，包括：
- WebSocket 连接状态
- 用户消息处理
- 状态机执行过程
- 错误信息

## 许可证

MIT License

## 贡献指南

欢迎提交 Issue 和 Pull Request！

## 联系方式

如有问题请联系开发团队。
